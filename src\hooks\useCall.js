"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import {
  useRTCClient,
  useLocalMicrophoneTrack,
  useRemoteUsers,
  useConnectionState,
  useJoin,
  usePublish,
  useClientEvent,
  useNetworkQuality,
  useIsConnected,
  RemoteAudioTrack,
  IAgoraRTCRemoteUser
} from "agora-rtc-react";
import { CallService } from "../services/CallService";
import { checkTokensBeforeCall } from "../services/AuthService";

// Define call status constants for better code readability
const CALL_STATUS = {
  IDLE: "idle",
  INCOMING: "incoming",
  RINGING: "ringing",
  CONNECTING: "connecting",
  CONNECTED: "connected",
  DISCONNECTED: "disconnected",
  ENDED: "ended",
  REJECTED: "rejected",
};

// Custom hook for managing call functionality
const useCall = (userId, rtmToken, rtcToken, user, eventData) => {
  // State variables
  const [callStatus, setCallStatus] = useState("idle");
  const [error, setError] = useState(null);
  const [activeCall, setActiveCall] = useState(null);
  const [incomingCall, setIncomingCall] = useState(null);
  const [isMuted, setIsMuted] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [callHistory, setCallHistory] = useState([]);
  const [localTrackState, setLocalTrackState] = useState({
    audioTrackMuted: false,
    audioTrackEnabled: true,
  });
  const [joinParams, setJoinParams] = useState(null);
  const [shouldConnect, setShouldConnect] = useState(false);
  const [callAccepted, setCallAccepted] = useState(false);
  const [joinSuccess, setJoinSuccess] = useState(false);
  const [publishSuccess, setPublishSuccess] = useState(false);
  const [connectingTimeout, setConnectingTimeout] = useState(null);
  const [rtcConnected, setRtcConnected] = useState(false);

  // References
  const callServiceRef = useRef(null);
  const callTimerRef = useRef(null);
  const currentCallRef = useRef(null);
  const localAudioTrackRef = useRef(null);

  // Get the Agora client from context
  const agoraClient = useRTCClient();
  const {
    localMicrophoneTrack,
    isLoading: isMicLoading,
    error: micError,
  } = useLocalMicrophoneTrack({
    AEC: true,       // Enable Acoustic Echo Cancellation
    AGC: true,       // Enable Automatic Gain Control
    ANS: true,       // Enable Automatic Noise Suppression
    encoderConfig: { // Use standard audio profile for voice calls
      sampleRate: 48000,
      stereo: false,
      bitrate: 48,
    }
  });
  const remoteUsers = useRemoteUsers();

  // Use Agora connection state hook
  const connectionState = useConnectionState();
  const isConnected = useIsConnected();
  const networkQuality = useNetworkQuality();

  // Setup join hook with dynamic join parameters
  const {
    isConnected: isJoined,
    isLoading: isJoining,
    error: joinError,
  } = useJoin(
    joinParams && joinParams.appid ? joinParams : null, // Make sure we don't pass incomplete parameters
    shouldConnect && joinParams && joinParams.appid, // Only join when we have valid parameters
    agoraClient
  );

  // Setup publish hook
  const { isPublished, error: publishError } = usePublish(
    localMicrophoneTrack ? [localMicrophoneTrack] : [],
    isJoined && localMicrophoneTrack && !isMicLoading
  );

  // Add reference for the chat service (for call activity tracking)
  const chatServiceRef = useRef(null);

  // Track when remote users' audio tracks are added
  useClientEvent(agoraClient, "user-published", (user, mediaType) => {
    if (mediaType === "audio") {
      console.log(`Remote user ${user.uid} published audio track`);
    }
  });

  // Track when remote users' audio tracks are removed
  useClientEvent(agoraClient, "user-unpublished", (user, mediaType) => {
    if (mediaType === "audio") {
      console.log(`Remote user ${user.uid} unpublished audio track`);
    }
  });

  // Listen for microphone changes and handle failures gracefully
  useEffect(() => {
    if (localMicrophoneTrack) {
      console.log("Local microphone track initialized successfully");
      localAudioTrackRef.current = localMicrophoneTrack;

      // Set up track-ended handler to detect if microphone is disconnected
      const handleTrackEnded = () => {
        console.error("Microphone track ended unexpectedly");
        setError("Microphone disconnected. Please check your device and try again.");

        // Since we can't directly recreate the track (would need to re-request microphone permissions),
        // we'll notify the user and suggest restarting the call
        if (callStatus === "connected" || callStatus === "connecting") {
          setError("Microphone access was lost. You may need to end the call and start again.");
        }
      };

      localMicrophoneTrack.on("track-ended", handleTrackEnded);

      return () => {
        localMicrophoneTrack.off("track-ended", handleTrackEnded);
      };
    }
  }, [localMicrophoneTrack, callStatus]);

  // Enhanced debug info
  useEffect(() => {
    // Log detailed state changes to help debug connection issues
    console.log("Call state updated:", {
      callStatus,
      isJoined,
      isPublished,
      callAccepted,
      connectionState,
      activeCall: activeCall
        ? `${activeCall.callId} (${
            activeCall.isOutgoing ? "outgoing" : "incoming"
          })`
        : "none",
      joinError: joinError ? joinError.message : null,
      publishError: publishError ? publishError.message : null,
      networkQuality: networkQuality
        ? `up:${networkQuality.uplinkNetworkQuality}, down:${networkQuality.downlinkNetworkQuality}`
        : "unknown",
      remoteUsers: remoteUsers.length,
    });
  }, [
    callStatus,
    isJoined,
    isPublished,
    callAccepted,
    connectionState,
    activeCall,
    joinError,
    publishError,
    networkQuality,
    remoteUsers.length,
  ]);

  // Listen for client events with improved logging
  useClientEvent(agoraClient, "connection-state-change", (state) => {
    console.log(`Agora connection state changed to: ${state}`);

    // Handle reconnection
    if (state === "DISCONNECTED" && callStatus === "connected") {
      setCallStatus("disconnected");
    }

    // Force callAccepted true if we're in a particular scenario where it might not be set
    // This helps cases where signaling may have succeeded but the flag wasn't set
    if (
      state === "CONNECTED" &&
      isPublished &&
      callStatus === "connecting" &&
      (activeCall?.isOutgoing === false ||
        (activeCall?.isOutgoing === true &&
          Date.now() - (activeCall?.startTime || 0) > 8000))
    ) {
      console.log(
        "Force setting callAccepted to true as we're connected but stuck"
      );
      setCallAccepted(true);
    }
  });

  // Separate effect to monitor join status
  useEffect(() => {
    if (isJoined && callStatus === "connecting") {
      console.log("Successfully joined channel");
    }
  }, [isJoined, callStatus]);

  // Separate effect to monitor publish status
  useEffect(() => {
    if (isPublished && callStatus === "connecting") {
      console.log("Successfully published audio track");
    }
  }, [isPublished, callStatus]);

  // Separate effect for call acceptance
  useEffect(() => {
    if (callAccepted && callStatus === "connecting") {
      console.log("Call has been accepted via signaling");
    }
  }, [callAccepted, callStatus]);

  // Watch for successful join, publish AND call acceptance to set connected state
  useEffect(() => {
    if (callStatus === "connecting" && isJoined && isPublished) {
      // If there's a RTC connection but call acceptance is taking too long, proceed anyway
      // This handles cases where signaling works but the flag isn't set properly
      const hasRtcConnectionButNotAccepted =
        !callAccepted && connectionState === "CONNECTED";
      const hasWaitedLongEnough =
        activeCall && Date.now() - activeCall.startTime > 10000;

      if (
        callAccepted ||
        (hasRtcConnectionButNotAccepted && hasWaitedLongEnough)
      ) {
        if (!callAccepted) {
          console.log(
            "Call not explicitly accepted, but RTC connected for 10+ seconds - forcing connection"
          );
          setCallAccepted(true);
        } else {
          console.log(
            "Successfully joined, published, and call accepted - call connected!"
          );
        }
        setCallStatus("connected");
      }
    }
  }, [
    callStatus,
    isJoined,
    isPublished,
    callAccepted,
    connectionState,
    activeCall,
  ]);

  // Add call heartbeat check to detect disconnections
  useEffect(() => {
    let heartbeatInterval;
    let missedHeartbeats = 0;
    const MAX_MISSED_HEARTBEATS = 3;

    // Only run heartbeat when in a connected call
    if (callStatus === "connected" && activeCall) {
      console.log("[HEARTBEAT] Starting call heartbeat check");

      // Check every 3 seconds if the other party is still connected
      heartbeatInterval = setInterval(() => {
        // If we have remote users, reset missed heartbeats counter
        if (remoteUsers && remoteUsers.length > 0) {
          if (missedHeartbeats > 0) {
            console.log(
              `[HEARTBEAT] Remote user present, resetting missed heartbeats`
            );
          }
          missedHeartbeats = 0;
          return;
        }

        // No remote users detected, increment counter
        missedHeartbeats++;
        console.log(
          `[HEARTBEAT] No remote users detected, missed heartbeats: ${missedHeartbeats}/${MAX_MISSED_HEARTBEATS}`
        );

        // If we've missed too many heartbeats, the call is likely dead
        if (missedHeartbeats >= MAX_MISSED_HEARTBEATS) {
          console.log(
            `[HEARTBEAT] Call appears to be disconnected, remote party likely left`
          );

          // Set call to disconnected state
          setCallStatus("disconnected");

          // Optional: automatically clean up after a delay
          // Instead of calling endCall directly, set a flag that will trigger cleanup
          setTimeout(() => {
            if (callStatus === "disconnected") {
              console.log(
                "[HEARTBEAT] Auto-ending disconnected call - setting cleanup flag"
              );
              // Don't call endCall() directly - will cause circular dependency
              // Just set a flag that will trigger the cleanup
              setCallStatus("heartbeat_cleanup_needed");
            }
          }, 3000);

          // Stop the heartbeat checks
          clearInterval(heartbeatInterval);
        }
      }, 3000);
    }

    return () => {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
      }
    };
  }, [callStatus, activeCall, remoteUsers]); // removed endCall from dependencies

  // Add handler for heartbeat cleanup state
  useEffect(() => {
    if (callStatus === "heartbeat_cleanup_needed" && activeCall) {
      console.log(
        "[HEARTBEAT_CLEANUP] Performing cleanup for disconnected call"
      );

      // Clean up call resources
      try {
        // Leave RTC channel if needed
        if (agoraClient && connectionState !== "DISCONNECTED") {
          console.log("[HEARTBEAT_CLEANUP] Leaving RTC channel");
          agoraClient
            .leave()
            .catch((err) =>
              console.warn("[HEARTBEAT_CLEANUP] Error leaving channel:", err)
          );
        }

        // Update call history
        if (activeCall) {
          const duration = Math.floor(
            (Date.now() - activeCall.startTime) / 1000
          );
          updateCallHistoryItem(activeCall.peerId, {
            duration,
            status: "disconnected",
          });
        }

        // Reset state
        setJoinParams(null);
        setShouldConnect(false);
        setCallStatus("idle");
        setCallDuration(0);
        setCallAccepted(false);
        setJoinSuccess(false);
        setPublishSuccess(false);
        setActiveCall(null);

        console.log("[HEARTBEAT_CLEANUP] Cleanup completed");
      } catch (err) {
        console.error("[HEARTBEAT_CLEANUP] Error during cleanup:", err);
        // Force reset state even if cleanup fails
        setCallStatus("idle");
        setActiveCall(null);
      }
    }
  }, [callStatus, activeCall, agoraClient, connectionState]);

  // Enhanced the call ended handler to ensure proper cleanup and feedback
  useEffect(() => {
    // Define the handler function
    const handleCallEnded = (callId) => {
      console.log(`[CRITICAL] Received call ended event for call ${callId}`);

      // Skip processing if we don't have an active call
      if (!activeCall) {
        console.log(`[CRITICAL] No active call to end for call ID ${callId}`);
        return;
      }

      // Skip processing if this is a video call (handled by useVideoCall)
      if (activeCall.isVideoCall) {
        console.log(`[CRITICAL] Ignoring end event for video call ${callId} in audio call handler`);
        return;
      }

      // Case 1: Handle active call being ended by remote
      if (
        activeCall.callId === callId &&
        (callStatus === "connected" || callStatus === "connecting")
      ) {
        console.log(
          `[CRITICAL] Processing end call event from remote side for active call ${callId}`
        );

        // Immediately update UI to show disconnected state
        setCallStatus("disconnected");

        try {
          // Send an acknowledgment of the end call signal if possible
          if (callServiceRef.current && activeCall.peerId) {
            console.log(
              `[END CALL] Sending end call acknowledgment to ${activeCall.peerId}`
            );
            callServiceRef.current
              .sendAckMessage(callId, activeCall.peerId, "end_call_ack")
              .catch((err) =>
                console.warn(`[END CALL] Error sending acknowledgment:`, err)
              );
          }
        } catch (err) {
          console.warn(`[END CALL] Error during acknowledgment:`, err);
        }

        // Set a timeout to show the "call ended by other party" message briefly before cleanup
        setTimeout(async () => {
          console.log("[CRITICAL] Cleaning up after remote party ended call");

          try {
            // Leave RTC channel if needed
            if (agoraClient && connectionState !== "DISCONNECTED") {
              console.log("[REMOTE END] Leaving RTC channel");
              await agoraClient.leave();
            }
          } catch (err) {
            console.error("[REMOTE END] Error leaving RTC channel:", err);
          }

          // Update call history with duration if we have an active call
          if (activeCall) {
            const duration = Math.floor(
              (Date.now() - activeCall.startTime) / 1000
            );
            updateCallHistoryItem(activeCall.peerId, {
              duration,
              status: "ended by remote",
            });
          }

          // Clean up state - make sure to do this in the correct order
          setShouldConnect(false);
          setJoinParams(null);
          setCallDuration(0);
          setCallAccepted(false);
          setJoinSuccess(false);
          setPublishSuccess(false);

          // Show brief notification about call ending
          setError("");

          // Reset call status to idle last to avoid UI flashing
          setCallStatus("idle");

          // Finally clear active call reference
          setActiveCall(null);

          // Record call duration for history
          const duration = Math.floor(
            (Date.now() - activeCall.startTime) / 1000
          );

          // Send call activity message via chat service
          if (chatServiceRef.current) {
            chatServiceRef.current
              .sendCallActivityMessage(activeCall.peerId, {
                type: "call_ended",
                duration: duration,
                callId: activeCall.callId,
                isOutgoing: activeCall.isOutgoing,
                timestamp: Date.now(),
              })
              .catch((err) =>
                console.error("Error sending call ended activity:", err)
              );
          }

          // Update call history with duration if we have an active call
          if (activeCall) {
            updateCallHistoryItem(activeCall.peerId, {
              duration,
              status: "ended by remote",
            });
          }

          // Don't auto-clear error so user can dismiss it manually
          // setTimeout(() => setError(null), 3000);
        }, 1500); // Show disconnected state for 1.5 seconds before cleanup
      }
      // Case 2: Handle incoming call being canceled before answering
      else if (incomingCall && incomingCall.callId === callId) {
        // Skip processing if this is a video call (handled by useVideoCall)
        if (incomingCall.isVideoCall) {
          console.log(`[CRITICAL] Ignoring cancel event for incoming video call ${callId} in audio call handler`);
          return;
        }
        console.log(
          `[CRITICAL] Caller canceled incoming call ${callId} before it was answered`
        );

        // Add to call history as missed call
        addToCallHistory({
          peerId: incomingCall.callerId,
          peerName: incomingCall.callerName,
          timestamp: Date.now(),
          duration: 0,
          isOutgoing: false,
          status: "caller canceled",
        });

        // Show a brief notification
        setError("Caller canceled the call");

        // Don't auto-clear error so user can dismiss it manually
        // setTimeout(() => setError(null), 3000);

        // Clear the incoming call state to close the modal
        setIncomingCall(null);

        // Send call activity message via chat service - but only once
        // Add a flag to the incomingCall object to track if we've already sent a missed call notification
        if (chatServiceRef.current && !incomingCall.missedCallNotificationSent) {
          chatServiceRef.current
            .sendCallActivityMessage(incomingCall.callerId, {
              type: "call_missed",
              callId: incomingCall.callId,
              isOutgoing: false,
              timestamp: Date.now(),
            })
            .catch((err) =>
              console.error("Error sending missed call activity:", err)
            );
        }
      }
    };

    // Register for call ended events
      if (callServiceRef.current) {
      console.log("[SETUP] Registering call ended event handler");
      callServiceRef.current.onCallEnded(handleCallEnded);
    }

    return () => {
      if (callServiceRef.current) {
        console.log("[CLEANUP] Removing call ended event handler");
        callServiceRef.current.offCallEnded(handleCallEnded);
      }
    };
  }, [activeCall, incomingCall, callStatus, agoraClient, connectionState]);

  // Initialize call service with better event handling
  useEffect(() => {
    // Skip initialization if any required parameter is missing
    if (!userId || !rtmToken) {
      console.log(
        "Skipping CallService initialization - missing required parameters"
      );
      return;
    }

    console.log("Getting CallService instance for", userId);

    // Use singleton pattern through static method
    callServiceRef.current = CallService.getInstance(userId, rtmToken);

      // Set up event listeners for the call service
    console.log("[AUDIO CALL] Setting up call listeners for audio calls");

    // Define the listener functions so we can reference them for cleanup
    const handleIncomingCall = (call) => {
      console.log("[SIGNAL] Incoming call received in audio call handler:", call);
      console.log("[SIGNAL] Call type:", call.isVideoCall ? "Video" : "Audio");

      // Only handle audio calls in this hook
      if (!call.isVideoCall) {
        console.log("[SIGNAL] Handling audio call in useCall hook");
        setIncomingCall(call);
      } else {
        console.log("[SIGNAL] Ignoring video call in audio call handler");
      }
    };

    const handleCallAccepted = (message) => {
      console.log("[SIGNAL] Call accepted for ID:", message.callId);

      // Only handle the acceptance if this is our active call and it's not a video call
      if (activeCall && activeCall.callId === message.callId && !activeCall.isVideoCall) {
        console.log(
          "[SIGNAL] Audio call signaling accepted, waiting for RTC connection..."
        );
        setCallAccepted(true);
      } else if (activeCall && activeCall.isVideoCall) {
        console.log("[SIGNAL] Ignoring video call acceptance in audio call handler");
      }
    };

    const handleCallRejected = (callId, reason) => {
      console.log("[SIGNAL] Call rejected:", callId, reason);

      // Only handle the rejection if this is our active call and it's not a video call
      if (activeCall && activeCall.callId === callId && !activeCall.isVideoCall) {
        console.log("[SIGNAL] Audio call rejected, cleaning up");
        setError(`Call was rejected${reason ? `: ${reason}` : ""}`);
        // Don't call endCall here directly - will cause circular dependency
        // Instead, set a flag that can trigger cleanup
        setCallStatus("rejected");
      } else if (activeCall && activeCall.isVideoCall) {
        console.log("[SIGNAL] Ignoring video call rejection in audio call handler");
      }
    };

    const handleCallEnded = (callId) => {
      console.log("[SIGNAL] Simple handler - Call ended by peer:", callId);

      // Only log for audio calls in this simple handler
      if (activeCall && activeCall.callId === callId && !activeCall.isVideoCall) {
        console.log("[SIGNAL] Audio call ended by peer (simple handler)");
      } else if (activeCall && activeCall.isVideoCall) {
        console.log("[SIGNAL] Ignoring video call end in audio call handler (simple handler)");
      }
      // Don't reference endCall here
    };

    // Register the listeners using the proper methods
    callServiceRef.current.onIncomingCall(handleIncomingCall);
    callServiceRef.current.onCallAccepted(handleCallAccepted);
    callServiceRef.current.onCallRejected(handleCallRejected);
    callServiceRef.current.onCallEnded(handleCallEnded);

    console.log("[AUDIO CALL] Successfully registered all audio call listeners");

      // Clean up function
      return () => {
        console.log("[AUDIO CALL] Cleaning up audio call listeners");

        // Properly unregister all listeners
        if (callServiceRef.current) {
          callServiceRef.current.offIncomingCall(handleIncomingCall);
          callServiceRef.current.offCallAccepted(handleCallAccepted);
          callServiceRef.current.offCallRejected(handleCallRejected);
          callServiceRef.current.offCallEnded(handleCallEnded);
          console.log("[AUDIO CALL] Successfully unregistered all audio call listeners");
        }

        // Don't clean up the CallService on unmount since we're using a singleton pattern
        // The service will be cleaned up by the getInstance static method when needed
        console.log("Component unmounting, but leaving CallService running");

        // But we should terminate any active call on unmount
        if (activeCall) {
          // Don't call endCall directly - will cause circular dependency
          // Just set the flags to trigger cleanup
          setJoinParams(null);
          setShouldConnect(false);
          setCallStatus("idle");
          setActiveCall(null);
        }
      };
  }, [userId, rtmToken, activeCall]); // removed endCall from dependency array

  // Add effect to handle call rejection state
  useEffect(() => {
    if (callStatus === "rejected" && activeCall) {
      console.log("[CLEANUP] Handling rejected call state");

      // Clean up resources
      if (agoraClient && connectionState !== "DISCONNECTED") {
        agoraClient
          .leave()
          .catch((err) =>
            console.warn(
              "[CLEANUP] Error leaving channel during rejection cleanup:",
              err
            )
        );
      }

      // Reset state
      setJoinParams(null);
      setShouldConnect(false);
      setActiveCall(null);
      setCallDuration(0);
      setCallAccepted(false);
      setCallStatus("idle");
    }
  }, [callStatus, activeCall, agoraClient, connectionState]);

  // Handle microphone errors
  useEffect(() => {
    if (micError) {
      console.error("Microphone error:", micError);
      setError(`Microphone error: ${micError.message}`);
    }
  }, [micError]);

  // Set up call timer when in active call
  useEffect(() => {
    if (callStatus === "connected") {
      setCallDuration(0);
      callTimerRef.current = setInterval(() => {
        setCallDuration((prev) => prev + 1);
      }, 1000);
    } else if (callStatus !== "connected" && callTimerRef.current) {
      clearInterval(callTimerRef.current);
      callTimerRef.current = null;
    }

    return () => {
      if (callTimerRef.current) {
        clearInterval(callTimerRef.current);
      }
    };
  }, [callStatus]);

  // Add a timeout for calls stuck in connecting
  useEffect(() => {
    let connectionTimeout;

    if (callStatus === "connecting") {
      // After 15 seconds of trying to connect, show a more specific error
      connectionTimeout = setTimeout(() => {
        if (callStatus === "connecting") {
          console.log("Call connection timeout - checking state");

          // Check what specifically failed
          if (!isJoined && joinError) {
            setError(`Failed to join channel: ${joinError.message}`);
          } else if (isJoined && !isPublished && publishError) {
            setError(`Failed to publish audio: ${publishError.message}`);
          } else if (
            networkQuality &&
            networkQuality.uplinkNetworkQuality > 3
          ) {
            setError(
              "Poor network connection. Please check your internet and try again."
            );
          } else {
            setError(
              "Call connection timed out. The other person may be unavailable."
            );
          }

          // Auto end the call after timeout
          endCall();
        }
      }, 15000);
    }

    return () => {
      if (connectionTimeout) {
        clearTimeout(connectionTimeout);
      }
    };
  }, [
    callStatus,
    isJoined,
    isPublished,
    joinError,
    publishError,
    networkQuality,
  ]);

  // Debug information for call state
  useEffect(() => {
    if (callStatus) {
      console.log(`[DEBUG] Call status changed: ${callStatus}. Details:`, {
        callAccepted,
        joinSuccess,
        publishSuccess,
        connectionState,
        networkQuality,
        callStatus,
      });
    }
  }, [
    callStatus,
    callAccepted,
    joinSuccess,
    publishSuccess,
    connectionState,
    networkQuality,
  ]);

  // Watch for connection state changes
  useEffect(() => {
    if (connectionState) {
      console.log(`[DEBUG] Connection state changed: ${connectionState}`);

      // If we are reconnected and still in a connecting state
      if (connectionState === "CONNECTED" && callStatus === "connecting") {
        console.log(
          "[DEBUG] RTC reconnected while still connecting - setting rtcConnected"
        );
        setRtcConnected(true);
      }
    }
  }, [connectionState, callStatus]);

  // Listen for client events
  useClientEvent(
    agoraClient,
    "connection-state-change",
    (curState, prevState) => {
      console.log(
        `[DEBUG] Client connection state changed from ${prevState} to ${curState}`
      );

      if (
        curState === "CONNECTED" &&
        callStatus === "connecting" &&
        callAccepted
      ) {
        console.log(
          "[CRITICAL] Forcing connected state on reconnection with call accepted"
        );
      // Give a small window for handling before forcing connection if we were in a call
            setTimeout(() => {
          if (callStatus === "connecting" && callAccepted) {
            setCallStatus("connected");
        }
      }, 2000);
    }
    }
  );

  // Watch for successful join
  useEffect(() => {
    if (joinSuccess) {
      console.log(
        `[DEBUG] Join successful - checking if we can mark as connected`
      );
      checkConnectionStatus();
    }
  }, [joinSuccess]);

  // Watch for successful publish
  useEffect(() => {
    if (publishSuccess) {
      console.log(
        `[DEBUG] Publish successful - checking if we can mark as connected`
      );
      checkConnectionStatus();
    }
  }, [publishSuccess]);

  // Watch for call acceptance
  useEffect(() => {
    if (callAccepted) {
      console.log(
        `[DEBUG] Call accepted - checking if we can mark as connected`
      );
      checkConnectionStatus();
    }
  }, [callAccepted]);

  // Function to check if all criteria for connection are met
  const checkConnectionStatus = () => {
    if (
      callStatus === "connecting" &&
      joinSuccess &&
      publishSuccess &&
      callAccepted
    ) {
      console.log(
        "[CRITICAL] All connection criteria met - setting call as connected"
      );
      setCallStatus("connected");

      // Clear any connecting timeout
      if (connectingTimeout) {
        clearTimeout(connectingTimeout);
        setConnectingTimeout(null);
      }
    }
  };

  // Reset function to fix stuck calls
  const resetCall = useCallback(() => {
    console.log("[CRITICAL] Resetting call state. Current state:", {
      callStatus,
      activeCall: activeCall
        ? `${activeCall.callId} (${
            activeCall.isOutgoing ? "outgoing" : "incoming"
          })`
        : "null",
      connectionState,
    });

    // Clean up any timers
    if (connectingTimeout) {
      clearTimeout(connectingTimeout);
      setConnectingTimeout(null);
    }

    // Reset join parameters to trigger leaving the channel
    setJoinParams(null);
    setShouldConnect(false);

    // Try to leave the channel if we're in one
    try {
      if (agoraClient && connectionState !== "DISCONNECTED") {
        console.log("[CRITICAL] Leaving channel during reset");
        agoraClient.leave().catch((err) => {
          console.warn("Non-critical error leaving channel during reset:", err);
        });
      }
      } catch (err) {
      console.warn("Non-critical error during channel leave:", err);
    }

    // Reset all state variables in the correct order
    setCallStatus("idle");
    setActiveCall(null);
    setIncomingCall(null);
    setCallDuration(0);
    setCallAccepted(false);
    setJoinSuccess(false);
    setPublishSuccess(false);
    setRtcConnected(false);
    setError(null);

    console.log("[CRITICAL] Call state has been fully reset");
  }, [callStatus, activeCall, connectionState, connectingTimeout, agoraClient]);

  // Helper function to leave Agora channel and clean up resources
  const leaveChannel = useCallback(async () => {
    console.log("[LEAVE] Using improved endCall for channel leaving");
    if (endCall) {
      return endCall();
    }
    return false;
  }, []);

  // This function is now handled directly in the onCallAccepted event handler
  // to avoid circular dependencies and ensure proper handling of video vs audio calls

  // Enhanced end call function with better cleanup and signaling
  const endCall = useCallback(async () => {
    console.log("[END CALL] Attempting to end call");

    if (!activeCall) {
      console.warn("[END CALL] No active call to end");
      return false;
    }

    try {
      // Record call duration for history
      const duration = Math.floor((Date.now() - activeCall.startTime) / 1000);

      // Send call activity message via chat service
      if (chatServiceRef.current) {
        chatServiceRef.current
          .sendCallActivityMessage(activeCall.peerId, {
            type: "call_ended",
            duration: duration,
            callId: activeCall.callId,
            isOutgoing: activeCall.isOutgoing,
            timestamp: Date.now(),
          })
          .catch((err) => console.error("Error sending call ended activity:", err));
      }

      try {
        // First, try to signal call end via RTM
        if (callServiceRef.current) {
          console.log(`[END CALL] Sending RTM end signal to ${activeCall.peerId}`);
              await callServiceRef.current.endCall(activeCall.callId, activeCall.peerId);
        }
      } catch (signalErr) {
        console.error("[END CALL] Error signaling call end:", signalErr);
          // Continue with cleanup even if signaling failed
      }

      // Leave the RTC channel to stop media
      try {
        if (agoraClient && (connectionState === "CONNECTED" || connectionState === "CONNECTING")) {
          console.log("[END CALL] Leaving RTC channel");
          await agoraClient.leave();
          console.log("[END CALL] Successfully left RTC channel");
        }
      } catch (err) {
        console.error("[END CALL] Error leaving RTC channel:", err);
        // Continue with cleanup even if channel leave failed
      }

      // Complete cleanup regardless of RTC channel leave success
      console.log("[END CALL] Completing call cleanup");

      // Reset join parameters to trigger proper disconnection
      setJoinParams(null);
      setShouldConnect(false);

      // Reset state variables in proper order
      setCallStatus("idle");
      setCallDuration(0);
      setCallAccepted(false);
      setJoinSuccess(false);
      setPublishSuccess(false);

      // Reset call object last to avoid UI flicker
      setActiveCall(null);

      console.log("[END CALL] Call successfully ended and cleaned up");

      return true;
    } catch (err) {
      console.error("[END CALL] Error ending call:", err);
      setError(`Failed to end call: ${err.message}`);

      // Force cleanup even if there was an error
      try {
        console.log("[END CALL] Forcing cleanup after error");

        // Leave channel if possible
        if (agoraClient) {
          try {
            await agoraClient.leave();
          } catch (leaveErr) {
            console.warn("[END CALL] Error during forced channel leave:", leaveErr);
          }
        }

        // Reset state
        setJoinParams(null);
        setShouldConnect(false);
        setCallStatus("idle");
        setCallDuration(0);
        setCallAccepted(false);
        setJoinSuccess(false);
        setPublishSuccess(false);
        setActiveCall(null);
      } catch (cleanupErr) {
        console.error("[END CALL] Error during forced cleanup:", cleanupErr);
      }

      return false;
    }
  }, [activeCall, agoraClient, connectionState]);

  // Function to start a call using hooks
  const startCall = useCallback(
    async (recipientId, recipientName) => {
      if (!userId || !rtcToken) {
        setError("Cannot start call: Missing user ID or token");
        return false;
      }

      if (!recipientId) {
        setError("Cannot start call: Missing recipient ID");
        return false;
      }

      try {
        console.log(`Starting call to ${recipientName} (${recipientId})`);

        // Check and refresh tokens before making the call
        try {
          console.log("Validating tokens before starting voice call...");
          await checkTokensBeforeCall(userId);
          console.log("Token validation successful");
        } catch (tokenError) {
          console.error("Token validation failed:", tokenError);
          setError(`Cannot start call: ${tokenError.message}`);
          return false;
        }

        setCallStatus("connecting");
        setError(null);

        // Generate a unique channel name for the call
        const channelName = `call_${userId}_${recipientId}_${Date.now()}`;
        const callId = `call_${Date.now()}`;

        // Create active call object first so event listeners can access it
        const newCall = {
          callId,
          channelName,
          peerId: recipientId,
          peerName: recipientName,
          startTime: Date.now(),
          isOutgoing: true,
        };

        setActiveCall(newCall);

        // Setup join parameters for useJoin hook
        // These options follow Agora's best practices for voice calls
        setJoinParams({
          appid: "4970bcf832df4acfa9a191eb5cbfcd7d",
          channel: channelName,
          token: rtcToken || null,
          uid: userId.toString(),
          // Set voice call optimization options
          optimizationMode: "VoiceCall", // Optimize for voice calls
          clientRoleType: "host", // Both users are hosts in a voice call
        });

        // Trigger the join operation
        setShouldConnect(true);

        // Send call request to recipient through signaling
        await callServiceRef.current.initiateCall({
          callId,
          channelName,
          recipientId,
          callerId: userId,
          callerName: user?.fullname || "Unknown",
          timestamp: Date.now(),
          isVideoCall: false, // Explicitly set to false for voice calls
        });

        // Send call activity message via chat service
        if (chatServiceRef.current) {
          chatServiceRef.current
            .sendCallActivityMessage(recipientId, {
              type: "call_started",
              callId: callId,
              isOutgoing: true,
              timestamp: Date.now(),
            })
            .catch((err) => console.error("Error sending call started activity:", err));
        }

        // Add to call history
        addToCallHistory({
          peerId: recipientId,
          peerName: recipientName,
          timestamp: Date.now(),
          duration: 0,
          isOutgoing: true,
          status: "connecting",
        });

        return true;
      } catch (err) {
        console.error("Error starting call:", err);
        setError(`Failed to start call: ${err.message}`);
        setCallStatus("idle");
        setActiveCall(null);
        await leaveChannel();
        return false;
      }
    },
    [userId, rtcToken, user?.fullname, leaveChannel]
  );

  // Function to answer an incoming call
  const answerCall = async () => {
    try {
      if (!incomingCall) {
        console.error("[CRITICAL] No incoming call to answer");
        return false;
      }

      console.log(`[CRITICAL] Answering call ${incomingCall.callId} from ${incomingCall.callerId}`);

      // Set active call information first
      const newCall = {
        callId: incomingCall.callId,
        channelName: incomingCall.channelName,
        peerId: incomingCall.callerId,
        peerName: incomingCall.callerName,
        startTime: Date.now(),
        isOutgoing: false,
        isVideoCall: false, // Explicitly set to false for voice calls
      };

      setActiveCall(newCall);

      // Setup join parameters BEFORE accepting the call
      // Make sure all required fields are present
      setJoinParams({
        appid: "4970bcf832df4acfa9a191eb5cbfcd7d",
        channel: incomingCall.channelName,
        token: rtcToken || null,
        uid: userId?.toString(),
        // Set voice call optimization options
        optimizationMode: "VoiceCall", // Optimize for voice calls
        clientRoleType: "host", // Both users are hosts in a voice call
      });

      // Set call status to connecting
      setCallStatus("connecting");

      // Set a timeout in case we get stuck in connecting
      const timeout = setTimeout(() => {
        if (callStatus === "connecting") {
          console.error("[CRITICAL] Call connect timeout reached - resetting call");
          resetCall();
        }
      }, 45000);

      setConnectingTimeout(timeout);

      // Accept the call (which will trigger handleCallAccepted)
      await callServiceRef.current.acceptCall(incomingCall.callId, incomingCall.callerId);

      console.log(`[CRITICAL] Successfully sent acceptance for call ${incomingCall.callId}`);

      // Handle call acceptance event for ourselves immediately
      // This helps with synchronization issues between caller and callee
      setCallAccepted(true);

      // Clear the incoming call reference to prevent auto-reject timer from firing
      setIncomingCall(null);

      // Trigger the join operation AFTER accepting the call
      setShouldConnect(true);

      // Send call activity message via chat service
      if (chatServiceRef.current) {
        chatServiceRef.current
          .sendCallActivityMessage(incomingCall.callerId, {
            type: "call_answered",
            callId: incomingCall.callId,
            isOutgoing: false,
            timestamp: Date.now(),
          })
          .catch((err) => console.error("Error sending call answered activity:", err));
      }
    } catch (error) {
      console.error("[CRITICAL] Error answering call:", error);
      setCallStatus("idle");
    }
  };

  // Function to toggle mute status
  const toggleMute = useCallback(async () => {
    if (!localMicrophoneTrack) return;

    try {
      console.log("Toggling mute:", !isMuted);

      // Use proper method to mute/unmute audio track
      if (isMuted) {
        await localMicrophoneTrack.setMuted(false);
        console.log("Microphone unmuted successfully");
      } else {
        await localMicrophoneTrack.setMuted(true);
        console.log("Microphone muted successfully");
      }

      setIsMuted(!isMuted);
      setLocalTrackState((prev) => ({
        ...prev,
        audioTrackMuted: !isMuted,
      }));
    } catch (err) {
      console.error("Error toggling mute:", err);
      setError(`Failed to toggle mute: ${err.message}`);
    }
  }, [localMicrophoneTrack, isMuted]);

  // Function to enable/disable audio track
  const setAudioEnabled = useCallback(
    async (state) => {
      if (!localMicrophoneTrack) return;

      try {
        console.log(`Setting audio enabled: ${state}`);

        // Use proper method to enable/disable audio track
        if (state) {
          await localMicrophoneTrack.setEnabled(true);
          // Also unmute if we're enabling the track
          await localMicrophoneTrack.setMuted(false);
          setIsMuted(false);
        } else {
          await localMicrophoneTrack.setEnabled(false);
        }

        setLocalTrackState((prev) => ({
          ...prev,
          audioTrackEnabled: state,
          audioTrackMuted: state ? false : prev.audioTrackMuted,
        }));
      } catch (err) {
        console.error("Error setting audio enabled:", err);
        setError(`Failed to change audio state: ${err.message}`);
      }
    },
    [localMicrophoneTrack]
  );

  // Helper function to add entry to call history
  const addToCallHistory = (callData) => {
    setCallHistory((prev) => [callData, ...prev.slice(0, 49)]); // Keep only last 50 calls
  };

  // Helper function to update an existing call history item
  const updateCallHistoryItem = (peerId, updates) => {
    setCallHistory((prev) =>
      prev.map((call) =>
        call.peerId === peerId &&
        call.timestamp === (activeCall?.startTime || 0)
          ? { ...call, ...updates }
          : call
      )
    );
  };

  // Clear any error that was set
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Format duration in MM:SS format
  const formatCallDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Function to reject incoming call
  const rejectCall = useCallback(async () => {
    if (!incomingCall) {
      return false;
    }

    try {
      console.log("Rejecting call from:", incomingCall.callerName);
      await callServiceRef.current.rejectCall(
        incomingCall.callId,
        incomingCall.callerId
      );

      // Send call activity message via chat service
      if (chatServiceRef.current) {
        chatServiceRef.current
          .sendCallActivityMessage(incomingCall.callerId, {
            type: "call_rejected",
            callId: incomingCall.callId,
            isOutgoing: false,
            timestamp: Date.now(),
          })
          .catch((err) =>
            console.error("Error sending call rejected activity:", err)
          );
      }

      // Add to call history
      addToCallHistory({
        peerId: incomingCall.callerId,
        peerName: incomingCall.callerName,
        timestamp: Date.now(),
        duration: 0,
        isOutgoing: false,
        status: "rejected",
      });

      setIncomingCall(null);
      return true;
    } catch (err) {
      console.error("Error rejecting call:", err);
      setError(`Failed to reject call: ${err.message}`);
      setIncomingCall(null);
      return false;
    }
  }, [incomingCall]);

  // Add timeout to automatically reset call data for incomingCall
  useEffect(() => {
    let incomingCallTimeout;

    if (incomingCall) {
      // Auto-reject if call not answered within 45 seconds
      incomingCallTimeout = setTimeout(() => {
        if (incomingCall) {
          console.log("Auto-rejecting incoming call that wasn't answered");
          rejectCall();
        }
      }, 45000);
    }

    return () => {
      if (incomingCallTimeout) {
        clearTimeout(incomingCallTimeout);
      }
    };
  }, [incomingCall, rejectCall]);

  // Monitor connection state changes and update call status accordingly
  useEffect(() => {
    if (!agoraClient) return;

    console.log(
      `[useCall] Setting up connection state change listener, current state: ${connectionState}`
    );

    const handleConnectionStateChange = (curState, prevState) => {
      console.log(
        `[useCall] AgoraRTC connection state changed: ${prevState} -> ${curState}`
      );

      if (
        curState === "CONNECTED" &&
        callStatus === "connecting" &&
        callAccepted
      ) {
        console.log(
          `[useCall] RTC connected and call accepted, setting status to CONNECTED`
        );
        setCallStatus(CALL_STATUS.CONNECTED);
      }

      if (curState === "DISCONNECTED" || curState === "CLOSED") {
        if (callStatus === CALL_STATUS.CONNECTED) {
          console.log(
            `[useCall] RTC disconnected while call was active, ending call`
          );
          setCallStatus(CALL_STATUS.ENDED);
          endCall();
        }
      }
    };

    agoraClient.on("connection-state-change", handleConnectionStateChange);

    return () => {
      agoraClient.off("connection-state-change", handleConnectionStateChange);
    };
  }, [agoraClient, connectionState, callStatus, callAccepted, endCall]);

  // Add a separate effect to monitor when both call is accepted and RTC is connected
  useEffect(() => {
    if (
      callAccepted &&
      connectionState === "CONNECTED" &&
      callStatus === CALL_STATUS.CONNECTING
    ) {
      console.log(
        `[useCall] Both call accepted and RTC connected, setting status to CONNECTED`
      );
      setCallStatus(CALL_STATUS.CONNECTED);
    }
  }, [callAccepted, connectionState, callStatus]);

  // Set current call ref whenever activeCall changes
  useEffect(() => {
    currentCallRef.current = activeCall;
    console.log(
      "[useCall] Updated currentCallRef:",
      activeCall?.callId || "none"
    );
  }, [activeCall]);

  // Initialize chat service hook prop reference
  useEffect(() => {
    // This will be set by the parent component that uses this hook
    // via the setChatService function we'll expose
  }, []);

  // Set chat service from parent component
  const setChatService = useCallback((chatService) => {
    if (
      chatService &&
      typeof chatService.sendCallActivityMessage === "function"
    ) {
      chatServiceRef.current = chatService;
      console.log("Chat service set for call activity tracking");
    }
  }, []);

  // Enhanced remote user audio track handling
  useEffect(() => {
    // Skip if no client or not connected
    if (!agoraClient || callStatus !== "connected") return;

    // Create a helper function to process a new remote audio track
    const processRemoteAudioTrack = (user, audioTrack) => {
      try {
        // Use RemoteAudioTrack features without type assertion
        const remoteTrack = audioTrack;

        // Set audio parameters for optimal voice quality
        remoteTrack.setVolume(100); // Set to 100% volume

        // Get current playback state
        const isPlaying = remoteTrack.isPlaying;

        if (!isPlaying) {
          remoteTrack.play();
          console.log(`Started playing remote user ${user.uid}'s audio with enhanced settings`);
        }

        // Add track event listeners for better monitoring
        remoteTrack.once("first-audio-frame", () => {
          console.log(`First audio frame received from user ${user.uid}`);
        });

        // Monitor if the track gets stopped for any reason
        remoteTrack.on("audio-stopped", () => {
          console.warn(`Remote audio from user ${user.uid} was stopped unexpectedly`);
          // Try to replay the track if it stops
          if (!remoteTrack.isPlaying) {
            setTimeout(() => {
              try {
                remoteTrack.play();
                console.log(`Successfully replayed remote user ${user.uid}'s audio after stop`);
              } catch (err) {
                console.error(`Failed to replay remote user ${user.uid}'s audio:`, err);
              }
            }, 1000);
          }
        });
      } catch (err) {
        console.error(`Error processing remote audio track for user ${user.uid}:`, err);
      }
    };

    // Process any existing remote users
    remoteUsers.forEach(user => {
      // Process user without type casting
      if (user.audioTrack) {
        processRemoteAudioTrack(user, user.audioTrack);
      }
    });

    // Set up event listener for future users
    const handleUserPublished = async (user, mediaType) => {
      console.log(`Remote user ${user.uid} published ${mediaType} track`);

      if (mediaType === "audio") {
        try {
          // Subscribe to remote user's audio track
          await agoraClient.subscribe(user, mediaType);
          console.log(`Subscribed to ${user.uid}'s audio track`);

          // Process the audio track if it exists
          if (user.audioTrack) {
            processRemoteAudioTrack(user, user.audioTrack);
          } else {
            console.warn(`Remote user ${user.uid} has no audio track after subscribing`);
          }
        } catch (err) {
          console.error(`Error subscribing to ${user.uid}'s audio:`, err);
        }
      }
    };

    // Other event handlers without type annotations
    const handleUserUnpublished = (user, mediaType) => {
      console.log(`Remote user ${user.uid} unpublished ${mediaType} track`);
    };

    const handleUserJoined = (user) => {
      console.log(`Remote user ${user.uid} joined the channel`);
    };

    const handleUserLeft = (user) => {
      console.log(`Remote user ${user.uid} left the channel`);
    };

    // Register event handlers
    agoraClient.on("user-published", handleUserPublished);
    agoraClient.on("user-unpublished", handleUserUnpublished);
    agoraClient.on("user-joined", handleUserJoined);
    agoraClient.on("user-left", handleUserLeft);

    // Clean up event handlers when component unmounts
    return () => {
      agoraClient.off("user-published", handleUserPublished);
      agoraClient.off("user-unpublished", handleUserUnpublished);
      agoraClient.off("user-joined", handleUserJoined);
      agoraClient.off("user-left", handleUserLeft);
    };
  }, [agoraClient, callStatus, remoteUsers]);

  // Automatically play remote audio tracks when they become available
  useEffect(() => {
    if (callStatus !== "connected") return;

    // Loop through all remote users and ensure their audio is playing
    remoteUsers.forEach(user => {
      if (user.audioTrack && !user.audioTrack.isPlaying) {
        console.log(`Auto-playing remote user ${user.uid}'s audio`);
        user.audioTrack.play();
      }
    });

  }, [remoteUsers, callStatus]);

  return {
    callStatus,
    error,
    activeCall,
    incomingCall,
    remoteUsers,
    isMuted,
    localTrackState,
    callDuration,
    callHistory,
    formatCallDuration,
    startCall,
    answerCall,
    rejectCall,
    endCall,
    toggleMute,
    setAudioEnabled,
    clearError,
    connectionState,
    networkQuality,
    resetCall,
    setChatService,
  };
};

export default useCall;
