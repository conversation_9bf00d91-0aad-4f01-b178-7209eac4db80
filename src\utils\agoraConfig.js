/**
 * Agora configuration utilities
 * This file contains helper functions for Agora RTC and RTM integration
 */

// Agora App ID - should be stored in environment variables in production
export const AGORA_APP_ID = "4970bcf832df4acfa9a191eb5cbfcd7d"

/**
 * Generate a random channel name for calls
 * @param {string} userId - Current user ID
 * @param {string} peerId - Peer user ID
 * @returns {string} - Unique channel name
 */
export const generateChannelName = (userId, peerId) => {
  // Sort IDs to ensure the same channel name regardless of who initiates
  const sortedIds = [userId, peerId].sort()
  return `call_${sortedIds[0]}_${sortedIds[1]}_${Date.now()}`
}

/**
 * Generate a unique call ID
 * @returns {string} - Unique call ID
 */
export const generateCallId = () => {
  return `call_${Date.now()}_${Math.floor(Math.random() * 1000)}`
}

/**
 * Check if browser supports required audio features
 * @returns {boolean} - Whether browser supports required features
 */
export const checkBrowserSupport = () => {
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia && window.RTCPeerConnection)
}

/**
 * Request microphone permissions
 * @returns {Promise<boolean>} - Whether permissions were granted
 */
export const requestMicrophonePermission = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })

    // Clean up the stream after checking permissions
    stream.getTracks().forEach((track) => track.stop())

    return true
  } catch (error) {
    console.error("Error requesting microphone permission:", error)
    return false
  }
}

/**
 * Format call duration in MM:SS format
 * @param {number} seconds - Duration in seconds
 * @returns {string} - Formatted duration
 */
export const formatCallDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
}
