import React, { createContext, useState, useEffect, useCallback, useMemo } from "react";
import { AuthService } from "../services/AuthService";

// Token refresh interval (50 minutes in milliseconds)
const TOKEN_REFRESH_INTERVAL = 50 * 60 * 1000;

// Token check interval (5 minutes in milliseconds)
// This is used to check if tokens need to be refreshed
const TOKEN_CHECK_INTERVAL = 5 * 60 * 1000;

// RTC token refresh interval (45 minutes in milliseconds)
const RTC_TOKEN_REFRESH_INTERVAL = 45 * 60 * 1000;

// Create the authentication context
export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [authData, setAuthData] = useState({
    user: null,
    event: null,
    tokens: null,
    eventSchedule: null,
    sponsors: null,
    rooms: null,
    attendees: null,
    isAuthenticated: false,
  });

  const [loading, setLoading] = useState(true);
  const [lastTokenRefresh, setLastTokenRefresh] = useState(Date.now());

  const login = async (username, password, meetingId) => {
    const data = await AuthService.login(username, password, meetingId);

    // Update last token refresh timestamp
    setLastTokenRefresh(Date.now());

    setAuthData({
      user: data.user,
      event: data.event,
      tokens: data.tokens,
      eventSchedule: data.eventSchedule,
      sponsors: data.sponsors,
      rooms: data.rooms,
      attendees: data.attendees,
      isAuthenticated: true,
    });
  };

  const logout = useCallback(async () => {
    await AuthService.logout();

    setAuthData({
      user: null,
      event: null,
      tokens: null,
      eventSchedule: null,
      sponsors: null,
      rooms: null,
      attendees: null,
      isAuthenticated: false,
    });
  }, []);

  // Function to refresh tokens
  const refreshTokens = useCallback(async () => {
    try {
      if (!authData.user?.agoraid) return false;

      console.log("Refreshing tokens...");
      const newTokens = await AuthService.refreshToken(authData.user.agoraid);

      if (newTokens) {
        setAuthData(prev => ({
          ...prev,
          tokens: newTokens
        }));

        // Update last token refresh timestamp
        setLastTokenRefresh(Date.now());

        return true;
      }
      return false;
    } catch (error) {
      console.error("Failed to refresh tokens:", error);
      return false;
    }
  }, [authData.user?.agoraid]);

  // Function to refresh only RTC token
  const refreshRTCToken = useCallback(async () => {
    try {
      if (!authData.user?.agoraid) return false;

      console.log("Refreshing RTC token...");
      const rtcToken = await AuthService.fetchRTCToken(authData.user.agoraid);

      if (rtcToken) {
        setAuthData(prev => ({
          ...prev,
          tokens: {
            ...prev.tokens,
            rtcToken,
            rtcTokenCreatedAt: Date.now()
          }
        }));
        return true;
      }
      return false;
    } catch (error) {
      console.error("Failed to refresh RTC token:", error);
      return false;
    }
  }, [authData.user?.agoraid]);

  // Restore login state on mount
  useEffect(() => {
    const restoreAuth = () => {
      try {
        const storedTokens = localStorage.getItem("tokens");
        const storedUser = localStorage.getItem("userData");
        const storedEvent = localStorage.getItem("eventData");
        const eventScheduleEvent = localStorage.getItem("eventSchedule");
        const sponsorsEvent = localStorage.getItem("sponsors");
        const roomsEvent = localStorage.getItem("rooms");
        const attendeesEvent = localStorage.getItem("attendees");

        if (storedTokens && storedUser) {
          setAuthData({
            user: JSON.parse(storedUser),
            event: storedEvent ? JSON.parse(storedEvent) : null,
            tokens: JSON.parse(storedTokens),
            eventSchedule: JSON.parse(eventScheduleEvent),
            sponsors: JSON.parse(sponsorsEvent),
            rooms: JSON.parse(roomsEvent),
            attendees: JSON.parse(attendeesEvent),
            isAuthenticated: true,
          });
        }
      } catch (error) {
        console.error("Failed to restore auth:", error);
        logout();
      } finally {
        setLoading(false);
      }
    };

    restoreAuth();
  }, [logout]);

  // Set up token refresh interval
  useEffect(() => {
    // Skip if not authenticated
    if (!authData.isAuthenticated || !authData.user?.agoraid) return;

    console.log("Setting up token refresh interval");

    // Function to check and refresh tokens if needed
    const checkAndRefreshTokens = async () => {
      try {
        // Refresh all tokens
        await refreshTokens();
      } catch (error) {
        console.error("Error in token refresh interval:", error);
      }
    };

    // Function to check and refresh only RTC token
    const checkAndRefreshRTCToken = async () => {
      try {
        // Check if we need to refresh RTC token
        const tokenData = localStorage.getItem("tokens");
        if (tokenData) {
          const tokens = JSON.parse(tokenData);
          const rtcTokenAge = tokens.rtcTokenCreatedAt ? Date.now() - tokens.rtcTokenCreatedAt : null;

          // If token is older than 45 minutes or doesn't exist, refresh it
          if (!tokens.rtcToken || !tokens.rtcTokenCreatedAt || rtcTokenAge > RTC_TOKEN_REFRESH_INTERVAL) {
            console.log("RTC token needs refresh, fetching new token...");
            await refreshRTCToken();
          }
        }
      } catch (error) {
        console.error("Error in RTC token refresh:", error);
      }
    };

    // Initial token refresh
    checkAndRefreshTokens();

    // Set up interval for regular token refresh
    const tokenIntervalId = setInterval(checkAndRefreshTokens, TOKEN_REFRESH_INTERVAL);

    // Set up interval for RTC token refresh (more frequent)
    const rtcTokenIntervalId = setInterval(checkAndRefreshRTCToken, RTC_TOKEN_REFRESH_INTERVAL);

    return () => {
      clearInterval(tokenIntervalId);
      clearInterval(rtcTokenIntervalId);
    };
  }, [authData.isAuthenticated, authData.user?.agoraid, refreshTokens, refreshRTCToken]);

  // Auth context value with useMemo to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    ...authData,
    login,
    logout,
    loading,
    refreshTokens,
    refreshRTCToken,
    lastTokenRefresh
  }), [authData, login, logout, loading, refreshTokens, refreshRTCToken, lastTokenRefresh]);

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};
